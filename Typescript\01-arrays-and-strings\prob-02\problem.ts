/**
 * Problem: Valid Palindrome
 * 
 * A phrase is a palindrome if, after converting all uppercase letters into lowercase letters 
 * and removing all non-alphanumeric characters, it reads the same forward and backward. 
 * Alphanumeric characters include letters and numbers.
 * 
 * Given a string s, return true if it is a palindrome, or false otherwise.
 * 
 * Example 1:
 * Input: s = "A man, a plan, a canal: Panama"
 * Output: true
 * Explanation: "amanaplanacanalpanama" is a palindrome.
 * 
 * Example 2:
 * Input: s = "race a car"
 * Output: false
 * Explanation: "raceacar" is not a palindrome.
 * 
 * Example 3:
 * Input: s = " "
 * Output: true
 * Explanation: s is an empty string "" after removing non-alphanumeric characters.
 * Since an empty string reads the same forward and backward, it is a palindrome.
 * 
 * Constraints:
 * - 1 <= s.length <= 2 * 105
 * - s consists only of printable ASCII characters.
 */

export function isPalindrome(s: string): boolean {
    // Convert to lowercase and remove non-alphanumeric characters
    const cleaned = s.toLowerCase().replace(/[^a-z0-9]/g, '');
    
    // Check if it's a palindrome using two pointers
    let left = 0;
    let right = cleaned.length - 1;
    
    while (left < right) {
        if (cleaned[left] !== cleaned[right]) {
            return false;
        }
        left++;
        right--;
    }
    
    return true;
}

// Alternative solution using built-in methods
export function isPalindromeBuiltIn(s: string): boolean {
    const cleaned = s.toLowerCase().replace(/[^a-z0-9]/g, '');
    return cleaned === cleaned.split('').reverse().join('');
}

// Recursive solution
export function isPalindromeRecursive(s: string): boolean {
    const cleaned = s.toLowerCase().replace(/[^a-z0-9]/g, '');
    
    if (cleaned.length <= 1) {
        return true;
    }
    
    if (cleaned[0] !== cleaned[cleaned.length - 1]) {
        return false;
    }
    
    return isPalindromeRecursive(cleaned.slice(1, -1));
} 