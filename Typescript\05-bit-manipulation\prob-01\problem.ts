/**
 * Problem: Number of 1 Bits (Hamming Weight)
 * 
 * Write a function that takes an unsigned integer and returns the number of '1' bits 
 * it has (also known as the Hamming weight).
 * 
 * Note:
 * - Note that in some languages, such as Java, there is no unsigned integer type. 
 *   In this case, the input will be given as a signed integer type. It should not 
 *   affect your implementation, as the integer's internal binary representation is 
 *   the same, whether it is signed or unsigned.
 * - In Java, the compiler represents the signed integers using 2's complement notation. 
 *   Therefore, in Example 3, the input represents the signed integer -3.
 * 
 * Example 1:
 * Input: n = 00000000000000000000000000001011
 * Output: 3
 * Explanation: The input binary string 00000000000000000000000000001011 has a total 
 * of three '1' bits.
 * 
 * Example 2:
 * Input: n = 00000000000000000000000010000000
 * Output: 1
 * Explanation: The input binary string 00000000000000000000000010000000 has a total 
 * of one '1' bit.
 * 
 * Example 3:
 * Input: n = 11111111111111111111111111111101
 * Output: 31
 * Explanation: The input binary string 11111111111111111111111111111101 has a total 
 * of thirty one '1' bits.
 * 
 * Constraints:
 * - The input must be a binary string of length 32.
 * 
 * Follow up: If this function is called many times, how would you optimize it?
 */

// Solution using bit manipulation with Brian Kernighan's algorithm
export function hammingWeight(n: number): number {
    let count = 0;
    
    while (n !== 0) {
        n = n & (n - 1); // This removes the least significant 1 bit
        count++;
    }
    
    return count;
}

// Solution using built-in method
export function hammingWeightBuiltIn(n: number): number {
    return n.toString(2).split('').filter(bit => bit === '1').length;
}

// Solution using bit shifting
export function hammingWeightBitShift(n: number): number {
    let count = 0;
    
    for (let i = 0; i < 32; i++) {
        if ((n & (1 << i)) !== 0) {
            count++;
        }
    }
    
    return count;
}

// Solution using lookup table (for optimization when called many times)
const LOOKUP_TABLE = new Array(256).fill(0).map((_, i) => {
    let count = 0;
    let num = i;
    while (num > 0) {
        count += num & 1;
        num >>= 1;
    }
    return count;
});

export function hammingWeightLookup(n: number): number {
    let count = 0;
    
    // Process 8 bits at a time
    for (let i = 0; i < 4; i++) {
        count += LOOKUP_TABLE[n & 0xFF];
        n >>= 8;
    }
    
    return count;
}

// Recursive solution
export function hammingWeightRecursive(n: number): number {
    if (n === 0) return 0;
    return (n & 1) + hammingWeightRecursive(n >>> 1);
} 