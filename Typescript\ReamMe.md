# TypeScript Coding Interview Practice Project

A comprehensive TypeScript project designed for practicing coding interview challenges, algorithms, and data structures. This project provides a structured approach to learning and mastering various programming concepts commonly tested in technical interviews.

## 🎯 Project Overview

This project is organized into 11 main categories, each focusing on specific programming concepts and problem-solving techniques. Each category contains sample problems with multiple solution approaches and comprehensive test suites.

### 📁 Project Structure

```
Typescript/
├── 01-arrays-and-strings/          # Array and String manipulation problems
│   └── prob-01/                    # Two Sum problem
│       ├── problem.ts              # Solution implementations
│       └── problem.test.ts         # Test cases
├── 02-linked-lists/                # Linked List data structure problems
│   └── prob-01/                    # Reverse Linked List problem
├── 03-stacks-and-queues/           # Stack and Queue problems
│   └── prob-01/                    # Valid Parentheses problem
├── 04-trees-and-graphs/            # Tree and Graph algorithms
│   └── prob-01/                    # Maximum Depth of Binary Tree
├── 05-bit-manipulation/            # Bit manipulation techniques
│   └── prob-01/                    # Number of 1 Bits (Hamming Weight)
├── 06-math-and-logic/              # Mathematical and logical problems
│   └── prob-01/                    # Fizz Buzz problem
├── 07-ood/                         # Object-Oriented Design problems
│   └── prob-01/                    # Parking Lot System Design
├── 08-recursion-and-dp/            # Recursion and Dynamic Programming
│   └── prob-01/                    # Climbing Stairs problem
├── 09-sorting-and-testing/         # Sorting algorithms and testing utilities
│   └── prob-01/                    # Multiple sorting algorithms with comparison
├── 10-advanced-topics/             # Advanced algorithms and data structures
│   └── prob-01/                    # Shortest Path algorithms and Red-Black Trees
├── 11-front-end/                   # Front-end specific problems
│   └── prob-01/                    # Virtual DOM and State Management
├── package.json                    # Project dependencies and scripts
├── tsconfig.json                   # TypeScript configuration
├── .eslintrc.js                    # ESLint configuration
├── .gitignore                      # Git ignore rules
└── README.md                       # This file
```

## 🚀 Quick Start

### Prerequisites

- **Node.js** (version 16 or higher)
- **npm** or **yarn** package manager
- **Git** (for version control)

### Installation Steps

1. **Clone the repository** (if using Git):
   ```bash
   git clone <repository-url>
   cd Typescript
   ```

2. **Install dependencies**:
   ```bash
   npm install
   ```

3. **Verify installation**:
   ```bash
   npm test
   ```

### Setup from Scratch on a New Machine

If you want to set up this project from scratch on a new machine:

1. **Create project directory**:
   ```bash
   mkdir typescript-coding-practice
   cd typescript-coding-practice
   ```

2. **Initialize npm project**:
   ```bash
   npm init -y
   ```

3. **Install TypeScript and development dependencies**:
   ```bash
   npm install --save-dev typescript @types/node @types/jest jest ts-jest ts-node eslint @typescript-eslint/eslint-plugin @typescript-eslint/parser
   ```

4. **Create TypeScript configuration** (`tsconfig.json`):
   ```json
   {
     "compilerOptions": {
       "target": "ES2020",
       "module": "commonjs",
       "lib": ["ES2020"],
       "outDir": "./dist",
       "rootDir": "./",
       "strict": true,
       "esModuleInterop": true,
       "skipLibCheck": true,
       "forceConsistentCasingInFileNames": true,
       "resolveJsonModule": true,
       "declaration": true,
       "declarationMap": true,
       "sourceMap": true,
       "removeComments": false,
       "noImplicitAny": true,
       "noImplicitReturns": true,
       "noImplicitThis": true,
       "noUnusedLocals": true,
       "noUnusedParameters": true,
       "exactOptionalPropertyTypes": true,
       "noImplicitOverride": true,
       "noPropertyAccessFromIndexSignature": true,
       "noUncheckedIndexedAccess": true
     },
     "include": ["**/*.ts"],
     "exclude": ["node_modules", "dist", "coverage"]
   }
   ```

5. **Create Jest configuration** (in `package.json`):
   ```json
   {
     "jest": {
       "preset": "ts-jest",
       "testEnvironment": "node",
       "testMatch": ["**/__tests__/**/*.ts", "**/?(*.)+(spec|test).ts"],
       "collectCoverageFrom": ["**/*.ts", "!**/*.d.ts", "!**/node_modules/**", "!**/coverage/**"]
     }
   }
   ```

6. **Create ESLint configuration** (`.eslintrc.js`):
   ```javascript
   module.exports = {
     parser: '@typescript-eslint/parser',
     plugins: ['@typescript-eslint'],
     extends: ['eslint:recommended', '@typescript-eslint/recommended'],
     env: {
       node: true,
       es6: true,
       jest: true,
     },
     parserOptions: {
       ecmaVersion: 2020,
       sourceType: 'module',
     },
     rules: {
       '@typescript-eslint/no-unused-vars': 'error',
       '@typescript-eslint/explicit-function-return-type': 'warn',
       '@typescript-eslint/no-explicit-any': 'warn',
       '@typescript-eslint/prefer-const': 'error',
       'no-console': 'off',
       'prefer-const': 'error',
       'no-var': 'error',
     },
   };
   ```

7. **Create `.gitignore`**:
   ```
   # Dependencies
   node_modules/
   npm-debug.log*
   yarn-debug.log*
   yarn-error.log*

   # Build outputs
   dist/
   build/
   *.tsbuildinfo

   # Testing
   coverage/
   .nyc_output/

   # Environment variables
   .env
   .env.local
   .env.development.local
   .env.test.local
   .env.production.local

   # IDE
   .vscode/
   .idea/
   *.swp
   *.swo
   *~

   # OS
   .DS_Store
   Thumbs.db

   # Logs
   logs
   *.log

   # Runtime data
   pids
   *.pid
   *.seed
   *.pid.lock

   # Optional npm cache directory
   .npm

   # Optional eslint cache
   .eslintcache

   # Output of 'npm pack'
   *.tgz

   # Yarn Integrity file
   .yarn-integrity
   ```

8. **Add scripts to `package.json`**:
   ```json
   {
     "scripts": {
       "test": "jest",
       "test:watch": "jest --watch",
       "test:coverage": "jest --coverage",
       "build": "tsc",
       "dev": "ts-node",
       "lint": "eslint . --ext .ts",
       "lint:fix": "eslint . --ext .ts --fix"
     }
   }
   ```

## 📚 Problem Categories

### 1. Arrays and Strings
- **Focus**: Array manipulation, string processing, two-pointer techniques
- **Sample Problem**: Two Sum (with multiple approaches: hash map, brute force)
- **Skills**: Hash maps, time complexity optimization

### 2. Linked Lists
- **Focus**: Linked list traversal, manipulation, and algorithms
- **Sample Problem**: Reverse Linked List (iterative, recursive, stack-based)
- **Skills**: Pointer manipulation, recursion

### 3. Stacks and Queues
- **Focus**: Stack and queue data structures, parentheses matching
- **Sample Problem**: Valid Parentheses (stack-based, recursive)
- **Skills**: Stack operations, bracket matching algorithms

### 4. Trees and Graphs
- **Focus**: Tree traversal, graph algorithms, BFS/DFS
- **Sample Problem**: Maximum Depth of Binary Tree (recursive, BFS, iterative DFS)
- **Skills**: Tree traversal, breadth-first search, depth-first search

### 5. Bit Manipulation
- **Focus**: Bitwise operations, bit counting, bit manipulation techniques
- **Sample Problem**: Number of 1 Bits (Hamming Weight) with multiple approaches
- **Skills**: Brian Kernighan's algorithm, bit shifting, lookup tables

### 6. Math and Logic
- **Focus**: Mathematical problems, logical thinking, pattern recognition
- **Sample Problem**: Fizz Buzz with multiple implementation approaches
- **Skills**: Mathematical thinking, extensible solutions

### 7. Object-Oriented Design (OOD)
- **Focus**: System design, class hierarchies, design patterns
- **Sample Problem**: Parking Lot System with multiple vehicle types
- **Skills**: Inheritance, polymorphism, factory pattern, builder pattern

### 8. Recursion and Dynamic Programming
- **Focus**: Recursive algorithms, memoization, dynamic programming
- **Sample Problem**: Climbing Stairs (recursive, memoization, DP, mathematical)
- **Skills**: Recursion, memoization, bottom-up DP, matrix exponentiation

### 9. Sorting and Testing
- **Focus**: Sorting algorithms, testing utilities, performance comparison
- **Sample Problem**: Multiple sorting algorithms with comprehensive testing
- **Skills**: Algorithm comparison, testing frameworks, performance analysis

### 10. Advanced Topics
- **Focus**: Advanced algorithms, complex data structures
- **Sample Problem**: Shortest path algorithms (Dijkstra, Bellman-Ford, Floyd-Warshall) and Red-Black Trees
- **Skills**: Graph algorithms, self-balancing trees, advanced data structures

### 11. Front-End Specific
- **Focus**: Front-end concepts, virtual DOM, state management
- **Sample Problem**: Virtual DOM implementation and Redux-like state management
- **Skills**: Virtual DOM, component systems, state management patterns

## 🛠️ Available Scripts

- **`npm test`**: Run all tests
- **`npm run test:watch`**: Run tests in watch mode
- **`npm run test:coverage`**: Run tests with coverage report
- **`npm run build`**: Compile TypeScript to JavaScript
- **`npm run dev`**: Run TypeScript directly with ts-node
- **`npm run lint`**: Run ESLint to check code quality
- **`npm run lint:fix`**: Run ESLint and automatically fix issues

## 🧪 Testing

Each problem includes comprehensive test suites that cover:
- **Edge cases**: Empty inputs, single elements, large datasets
- **Multiple approaches**: Different solution methods for the same problem
- **Performance testing**: Time and space complexity validation
- **Integration tests**: End-to-end functionality verification

### Running Tests

```bash
# Run all tests
npm test

# Run tests for a specific problem
npm test -- 01-arrays-and-strings/prob-01/problem.test.ts

# Run tests in watch mode
npm run test:watch

# Generate coverage report
npm run test:coverage
```

## 📝 Adding New Problems

To add a new problem to any category:

1. **Create problem directory**:
   ```bash
   mkdir 01-arrays-and-strings/prob-02
   ```

2. **Create problem file** (`problem.ts`):
   ```typescript
   // Problem description
   export function solution(input: any): any {
     // Your implementation here
   }
   ```

3. **Create test file** (`problem.test.ts`):
   ```typescript
   import { solution } from './problem';

   describe('Problem Name', () => {
     test('should handle basic case', () => {
       expect(solution(input)).toBe(expectedOutput);
     });
   });
   ```

## 🎯 Learning Path

### Beginner Level
1. Start with **Arrays and Strings** (01)
2. Move to **Math and Logic** (06)
3. Practice **Stacks and Queues** (03)

### Intermediate Level
1. **Linked Lists** (02)
2. **Trees and Graphs** (04)
3. **Bit Manipulation** (05)
4. **Recursion and DP** (08)

### Advanced Level
1. **Object-Oriented Design** (07)
2. **Sorting and Testing** (09)
3. **Advanced Topics** (10)
4. **Front-End Specific** (11)

## 🔧 Development Tips

### Code Quality
- Use TypeScript's strict mode for better type safety
- Follow ESLint rules for consistent code style
- Write comprehensive tests for all solutions
- Document your code with JSDoc comments

### Problem Solving Approach
1. **Understand the problem** thoroughly
2. **Write test cases** first (TDD approach)
3. **Implement brute force** solution if needed
4. **Optimize** for time and space complexity
5. **Test** with edge cases and large inputs
6. **Document** your approach and reasoning

### Performance Considerations
- Always analyze time and space complexity
- Consider multiple approaches for each problem
- Use appropriate data structures
- Profile your solutions with large inputs

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Add your problem solution
4. Write comprehensive tests
5. Ensure all tests pass
6. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

If you encounter any issues or have questions:
1. Check the existing test cases for examples
2. Review the TypeScript and Jest documentation
3. Create an issue in the repository

## 🎉 Acknowledgments

This project is designed to help developers prepare for technical interviews by providing:
- Structured learning path
- Multiple solution approaches
- Comprehensive testing
- Real-world problem scenarios
- Performance optimization techniques

Happy coding and good luck with your interviews! 🚀
