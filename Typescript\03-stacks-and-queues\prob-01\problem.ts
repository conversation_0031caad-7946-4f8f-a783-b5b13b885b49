/**
 * Problem: Valid Parentheses
 * 
 * Given a string s containing just the characters '(', ')', '{', '}', '[' and ']', 
 * determine if the input string is valid.
 * 
 * An input string is valid if:
 * 1. Open brackets must be closed by the same type of brackets.
 * 2. Open brackets must be closed in the correct order.
 * 3. Every close bracket has a corresponding open bracket of the same type.
 * 
 * Example 1:
 * Input: s = "()"
 * Output: true
 * 
 * Example 2:
 * Input: s = "()[]{}"
 * Output: true
 * 
 * Example 3:
 * Input: s = "(]"
 * Output: false
 * 
 * Example 4:
 * Input: s = "([)]"
 * Output: false
 * 
 * Example 5:
 * Input: s = "{[]}"
 * Output: true
 * 
 * Constraints:
 * - 1 <= s.length <= 104
 * - s consists of parentheses only '()[]{}'
 */

export function isValid(s: string): boolean {
    const stack: string[] = [];
    const brackets: { [key: string]: string } = {
        ')': '(',
        '}': '{',
        ']': '['
    };
    
    for (const char of s) {
        if (char === '(' || char === '{' || char === '[') {
            stack.push(char);
        } else {
            if (stack.length === 0 || stack.pop() !== brackets[char]) {
                return false;
            }
        }
    }
    
    return stack.length === 0;
}

// Alternative solution using Map
export function isValidMap(s: string): boolean {
    const stack: string[] = [];
    const brackets = new Map([
        [')', '('],
        ['}', '{'],
        [']', '[']
    ]);
    
    for (const char of s) {
        if (!brackets.has(char)) {
            stack.push(char);
        } else {
            if (stack.length === 0 || stack.pop() !== brackets.get(char)) {
                return false;
            }
        }
    }
    
    return stack.length === 0;
}

// Recursive solution (less efficient but educational)
export function isValidRecursive(s: string): boolean {
    if (s.length === 0) return true;
    if (s.length % 2 !== 0) return false;
    
    const pairs = ['()', '[]', '{}'];
    
    for (const pair of pairs) {
        const index = s.indexOf(pair);
        if (index !== -1) {
            const newString = s.slice(0, index) + s.slice(index + 2);
            if (isValidRecursive(newString)) {
                return true;
            }
        }
    }
    
    return false;
} 