{"name": "typescript-coding-interviews", "version": "1.0.0", "description": "A comprehensive TypeScript project for practicing coding interview challenges", "main": "index.js", "scripts": {"test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "build": "tsc", "dev": "ts-node", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix"}, "keywords": ["typescript", "algorithms", "data-structures", "coding-interviews", "jest", "testing"], "author": "", "license": "MIT", "devDependencies": {"@types/jest": "^29.5.8", "@types/node": "^20.8.10", "@typescript-eslint/eslint-plugin": "^6.9.1", "@typescript-eslint/parser": "^6.9.1", "eslint": "^8.53.0", "jest": "^29.7.0", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "typescript": "^5.2.2"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "testMatch": ["**/__tests__/**/*.ts", "**/?(*.)+(spec|test).ts"], "collectCoverageFrom": ["**/*.ts", "!**/*.d.ts", "!**/node_modules/**", "!**/coverage/**"]}}